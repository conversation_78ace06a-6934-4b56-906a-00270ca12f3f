name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  BUN_VERSION: '1.0.35'
  POSTGRES_DB: test_db
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  DATABASE_URL: postgresql://postgres:postgres@localhost:5433/test_db
  REDIS_URL: redis://localhost:6380

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:alpine
        env:
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432

      redis:
        image: redis:alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6380:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Create test environment
        run: |
          cat > .env.test << EOF
          NODE_ENV=test
          DATABASE_URL=${{ env.DATABASE_URL }}
          REDIS_URL=${{ env.REDIS_URL }}
          JWT_SECRET=test-jwt-secret-key-for-testing-only
          JWT_EXPIRES_IN=1h
          EMAIL_FROM=<EMAIL>
          SMTP_HOST=localhost
          SMTP_PORT=1025
          SMTP_SECURE=false
          SMTP_USER=
          SMTP_PASS=
          GOOGLE_OAUTH_ENABLED=false
          GOOGLE_CLIENT_ID=test-google-client-id
          GOOGLE_CLIENT_SECRET=test-google-client-secret
          FACEBOOK_OAUTH_ENABLED=false
          FACEBOOK_CLIENT_ID=test-facebook-client-id
          FACEBOOK_CLIENT_SECRET=test-facebook-client-secret
          FRONTEND_URL=http://localhost:3000
          REQUIRE_EMAIL_VERIFICATION=false
          PORT=3001
          API_PREFIX=api
          CORS_ORIGIN=http://localhost:3000
          LOG_LEVEL=error
          BCRYPT_ROUNDS=4
          RATE_LIMIT_TTL=60
          RATE_LIMIT_LIMIT=1000
          GRAPHQL_PLAYGROUND=false
          GRAPHQL_INTROSPECTION=true
          EOF

      - name: Wait for database
        run: |
          echo "Waiting for PostgreSQL..."
          timeout 60 bash -c 'until docker exec $(docker ps -q --filter "ancestor=postgres:alpine") pg_isready -U ${{ env.POSTGRES_USER }}; do sleep 2; done'
          echo "PostgreSQL is ready!"

      - name: Setup database
        run: |
          bun run db:generate
          bun run db:migrate

      - name: Run linting
        run: bun run lint

      - name: Run tests
        run: bun run test:all
        env:
          NODE_ENV: test
          DATABASE_URL: ${{ env.DATABASE_URL }}
          REDIS_URL: ${{ env.REDIS_URL }}

      - name: Upload coverage
        uses: actions/upload-artifact@v4
        if: success()
        with:
          name: coverage-report
          path: coverage/
          retention-days: 30

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run security audit
        run: bun audit

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build application
        run: bun run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/
          retention-days: 30
