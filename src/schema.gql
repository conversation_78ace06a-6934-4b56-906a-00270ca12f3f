# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

directive @upper on FIELD_DEFINITION

type AuthPayload {
  token: String!
  user: UserModel!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input LoginInput {
  email: String!
  password: String!
}

type Mutation {
  deleteUser(id: String!): Boolean!
  login(input: LoginInput!): AuthPayload!
  loginWithOAuth(code: String!, provider: String!): AuthPayload!
  logout: Boolean!
  register(input: RegisterInput!): AuthPayload!
  requestPasswordReset(input: RequestPasswordResetInput!): <PERSON><PERSON><PERSON>!
  resetPassword(input: ResetPasswordInput!): Boolean!
  updateUser(id: String!, input: UpdateUserInput!): UserModel!
}

type Query {
  getOAuthProviders: [String!]!
  getOAuthUrl(provider: String!): String!
  me: UserModel
  user(id: String!): UserModel!
  users: [UserModel!]!
  validateToken(token: String!): Boolean!
}

input RegisterInput {
  email: String!
  name: String!
  password: String!
}

input RequestPasswordResetInput {
  email: String!
}

input ResetPasswordInput {
  newPassword: String!
  token: String!
}

input UpdateUserInput {
  isActive: Boolean
  name: String
}

type UserModel {
  createdAt: DateTime!
  email: String!
  id: ID!
  isActive: Boolean!
  name: String!
  role: UserRole!
  updatedAt: DateTime!
}

"""User role enumeration"""
enum UserRole {
  ADMIN
  MODERATOR
  USER
}