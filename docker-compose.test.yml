version: '3.8'

services:
  postgres-test:
    image: postgres:alpine
    container_name: postgres-test
    restart: "no"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=test_db
    ports:
      - "5432:5432" # Different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 5s
      retries: 5

  redis-test:
    image: redis:alpine
    container_name: redis-test
    restart: "no"
    ports:
      - "6380:6379" # Different port to avoid conflicts
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_test_data:
  redis_test_data:


networks:
  default:
    name: test-network
