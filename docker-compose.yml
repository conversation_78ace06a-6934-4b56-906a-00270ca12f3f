services:
  postgres:
    image: postgres:alpine
    container_name: postgres
    restart: always
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - 5432:5432
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    container_name: redis
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data

networks:
  default:
    name: nestjs-graphql-starter-network

volumes:
  postgres_data:
  redis_data:
