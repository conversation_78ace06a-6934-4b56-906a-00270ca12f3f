{"name": "nestjs-graphql-starter", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:unit": "jest --testPathPattern=test/unit", "test:integration": "jest --testPathPattern=test/graphql --testPathPattern=test/rest", "test:all": "bun run test:unit && bun run test:integration && bun run test:e2e", "test:setup": "./scripts/setup-test-db.sh", "test:teardown": "docker-compose -f docker-compose.test.yml down -v", "test:full": "bun run test:setup && bun run test:all && bun run test:teardown", "test:run": "./scripts/run-tests.sh", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "db:generate:test": "drizzle-kit generate --config=drizzle.config.test.ts", "db:push:test": "drizzle-kit push --config=drizzle.config.test.ts", "db:studio:test": "drizzle-kit studio --config=drizzle.config.test.ts", "dev:setup": "./scripts/dev-setup.sh", "dev:reset": "bun db:drop && bun db:migrate && bun db:generate", "dev:clean": "rm -rf dist coverage .nyc_output", "dev:full": "bun dev:clean && bun install && bun dev:setup", "health:check": "curl -f http://localhost:3000/health || echo 'Health check failed'", "queue:status": "curl -s http://localhost:3000/health/detailed?includeQueue=true | jq .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "drizzle:setup": "chmod +x ./scripts/setup-drizzle-studio.sh && ./scripts/setup-drizzle-studio.sh", "better-auth:setup": "chmod +x ./scripts/setup-better-auth.sh && ./scripts/setup-better-auth.sh"}, "dependencies": {"@apollo/server": "^4.11.3", "@graphql-tools/utils": "^10.8.6", "@nestjs/apollo": "^13.1.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@react-email/components": "^0.1.1", "@react-email/render": "^1.1.3", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "better-auth-mikro-orm": "^0.4.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^17.0.0", "drizzle-orm": "^0.44.2", "graphql": "^16.11.0", "graphql-query-complexity": "^1.1.0", "graphql-subscriptions": "^2.0.0", "jsonwebtoken": "^9.0.2", "nest-winston": "^1.10.2", "nodemailer": "^7.0.4", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "pg-boss": "^10.3.2", "postgres": "^3.4.7", "react": "^19.1.0", "react-email": "^4.0.17", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "winston": "^3.17.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.2", "drizzle-kit": "^0.31.4", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts", "tsx"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest", "^.+\\.(t|j)sx$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^react$": "<rootDir>/../node_modules/react", "^@react-email/components$": "<rootDir>/../test/mocks/react-email-mock.ts"}, "setupFilesAfterEnv": ["<rootDir>/../test/setup/jest-setup.ts"]}}